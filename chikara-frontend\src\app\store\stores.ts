import { createTrackedSelector } from "react-tracked";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import { normalStore } from "./normalStore";
import { persistStore } from "./persistStore";
import { sessionStore } from "./sessionStore";
import { socketStore } from "./socketStore";

export const usePersistStore = createTrackedSelector(persistStore);

export const useSocketStore = createTrackedSelector(socketStore);

export const useSessionStore = createTrackedSelector(sessionStore);

export const useNormalStore = createTrackedSelector(normalStore);

interface AuthState {
    authed: boolean;
    setAuthed: (authed: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            authed: false,
            setAuthed: (authed) => set(() => ({ authed })),
        }),
        {
            name: "auth",
            version: 1.1,
        }
    )
);
