import { APIROUTES } from "@/helpers/apiRoutes";
import { handleGet } from "@/helpers/axiosInstance";
import type { User } from "@/types/user";
import { useQuery } from "@tanstack/react-query";
import { fetchGameConfig } from "../../app/fetchGameConfig";
import { useAuthStore, usePersistStore } from "../../app/store/stores";

export interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
    [key: string]: unknown;
}

const useFetchCurrentUser = (options: QueryOptions = {}) => {
    const { staleCurrentUserData, setStaleCurrentUserData, gameConfig } = usePersistStore();
    const authed = useAuthStore((state) => state.authed);

    const fetchUser = async (): Promise<User> => {
        const response = await handleGet<User>(APIROUTES.USER.CURRENTUSERINFO);

        if (!response) {
            throw new Error("No user data received");
        }

        if (response !== staleCurrentUserData) {
            setStaleCurrentUserData(response);
        }

        // Update game config if it doesn't exist or if the version is different
        if (!gameConfig || gameConfig.version !== response.gameConfigVersion) {
            // Non blocking
            fetchGameConfig(response.gameConfigVersion);
        }

        return response;
    };

    return useQuery<User>({
        queryKey: [APIROUTES.USER.CURRENTUSERINFO],
        queryFn: fetchUser,
        placeholderData: staleCurrentUserData,
        enabled: !!authed,
        staleTime: 30000,
        ...options,
    });
};

export default useFetchCurrentUser;

