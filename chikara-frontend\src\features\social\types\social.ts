import type { AppRouterClient } from "@/lib/orpc";

export type FriendListResponse = Awaited<ReturnType<AppRouterClient["social"]["getFriends"]>>;
export type Friend = FriendListResponse[number];

export type FriendRequestResponse = Awaited<ReturnType<AppRouterClient["social"]["getFriendRequests"]>>;
export type FriendRequest = FriendRequestResponse[number];

export type RivalListResponse = Awaited<ReturnType<AppRouterClient["social"]["getRivals"]>>;
export type Rival = RivalListResponse[number];

export type StatusMessageResponse = Awaited<ReturnType<AppRouterClient["social"]["getStatusMessage"]>>;
export type PrivacySettingsResponse = Awaited<ReturnType<AppRouterClient["social"]["getPrivacySettings"]>>;
